{"name": "epic-padel-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-ses": "^3.826.0", "@microsoft/clarity": "^1.0.0", "@radix-ui/react-dialog": "^1.1.14", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.9.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "firebase": "^11.8.1", "framer-motion": "^12.12.1", "gsap": "^3.13.0", "lucide-react": "^0.511.0", "next": "15.3.2", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-toastify": "^10.0.6", "sonner": "^2.0.5", "swr": "^2.3.4", "tailwind-merge": "^3.3.0", "zod": "^3.25.51", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "typescript": "^5"}}