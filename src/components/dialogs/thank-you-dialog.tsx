"use client";

import { checkoutTransactionStatus } from "@/api/payment-service";
import { useFacebookPixel } from "@/hooks/use-facebook-pixel";
import { extractErrorMessage } from "@/libs/utils";
import { useUIStore } from "@/store/ui-store";
import * as Dialog from "@radix-ui/react-dialog";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";

interface ThankYouDialogProps {}

const MAX_RETRIES = 5;
const RETRY_DELAY = 10000; // 10 seconds

const ThankYouDialog: React.FC<ThankYouDialogProps> = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { trackSubscribe } = useFacebookPixel();
  const isPaymentSuccess = searchParams.get("success") === "true";
  const transactionId = searchParams.get("transaction_id");
  const { isThankYouDialogOpen, closeThankYouDialog, openThankYouDialog } = useUIStore();

  const [status, setStatus] = useState<string | null>("processing");
  const [subscription, setSubscription] = useState<any>(null);
  const [failMessage, setFailMessage] = useState<string | null>(null);
  const retryCount = useRef(0);
  const retryTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (searchParams.get("success") || searchParams.get("transaction_id")) {
      openThankYouDialog();
    }
  }, [isPaymentSuccess]);

  useEffect(() => {
    if (searchParams.get("transaction_id")) {
      openThankYouDialog();
      getTransactionStatus();
    } else {
      setStatus("failed");
    }
    return () => {
      if (retryTimeout.current) clearTimeout(retryTimeout.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      closeThankYouDialog();
    }
  };

  const handlePlanDetails = () => {
    router.push("/profile?section=subscriptions");
    closeThankYouDialog();
  };

  const getTransactionStatus = async () => {
    try {
      const res = await checkoutTransactionStatus({ transactionId });
      const response = res?.data;
      const status = response?.data?.status;
      console.log("status", status);
      if (status === "true") {
        setStatus("success");

        setSubscription(response?.data?.subscription);
        trackSubscribe();
        // sendFacebookConversionApiEvent("Subscribe", {
        //   email: "<EMAIL>",
        //   phone: "1234567890",
        // });
      } else if (status === "false") {
        setStatus("failed");
        setFailMessage(extractErrorMessage(response?.data) || "Failed to save payment details");
      } else if (status === "processing") {
        setStatus("processing");
        if (retryCount.current < MAX_RETRIES) {
          retryCount.current += 1;
          retryTimeout.current = setTimeout(getTransactionStatus, RETRY_DELAY);
        } else {
          setFailMessage(
            "Payment is still processing. Please check your subscription status later or contact support."
          );
          setStatus("failed");
        }
      } else {
        setStatus("failed");
        setFailMessage("Unknown payment status");
      }
    } catch (error: any) {
      setStatus("failed");
      setFailMessage(extractErrorMessage(error) || "Failed to load payment status");
    }
  };

  return (
    <Dialog.Root open={isThankYouDialogOpen} onOpenChange={handleOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-md translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-6 shadow-lg duration-200">
          <Dialog.Title className="sr-only">
            {status === "success"
              ? "Thank You"
              : status === "processing"
                ? "Processing Payment"
                : "Payment Failed"}
          </Dialog.Title>
          <Dialog.Description className="sr-only">
            {status === "success"
              ? "You've successfully activated your plan. We're excited to have you on board!"
              : status === "processing"
                ? "Your payment is being processed. Please wait..."
                : failMessage ||
                  "Your payment was not successful. Please try again or contact support if the issue persists."}
          </Dialog.Description>
          <div className="flex flex-col items-center text-center">
            {status === "processing" && (
              <>
                <h6 className="mb-4 text-2xl font-semibold text-gray-900">⏳ Processing Payment</h6>
                <p className="mb-6 text-gray-600">
                  Your payment is being processed. Please wait...
                </p>
              </>
            )}
            {status === "success" && subscription && (
              <>
                <h6 className="mb-4 text-2xl font-semibold text-gray-900">🎉 Thank You!</h6>
                <p className="mb-6 text-gray-600">
                  You've successfully activated your plan. We're excited to have you on board!
                </p>
                <div className="mb-6 w-full max-w-xs rounded-lg bg-gray-100 p-4 text-left">
                  <div className="mb-2">
                    <span className="font-semibold text-gray-800">Plan:</span>{" "}
                    {subscription?.package?.name || "No Plan"}
                  </div>
                  <div className="mb-2">
                    <span className="font-semibold text-gray-800">Start Date:</span>{" "}
                    {subscription?.start_date || "N/A"}
                  </div>
                  <div>
                    <span className="font-semibold text-gray-800">End Date:</span>{" "}
                    {subscription?.end_date || "N/A"}
                  </div>
                </div>
                <button
                  onClick={handlePlanDetails}
                  className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full px-6 py-2 text-sm font-medium text-white transition-colors focus:ring-2 focus:outline-none"
                >
                  Plan Details
                </button>
              </>
            )}
            {status === "failed" && (
              <>
                <h6 className="mb-4 text-2xl font-semibold text-red-600">
                  ❌ Failed to save payment details
                </h6>
                <p className="failed-message mb-6 text-gray-600">
                  {failMessage ||
                    "Your payment details was not saved successfully. Please try again or contact support if the issue persists."}
                </p>
                <button
                  onClick={() => {
                    router.push("/contact");
                    closeThankYouDialog();
                  }}
                  className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full px-6 py-2 text-sm font-medium text-white transition-colors focus:ring-2 focus:outline-none"
                >
                  Contact Support
                </button>
              </>
            )}
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ThankYouDialog;
