"use client";
import { Location } from "@/app/locations/[slug]/page";
import React, { useRef } from "react";

const LocationSection = ({ location }: { location: Location }) => {
  const mapSectionRef = useRef<HTMLDivElement>(null);
  return (
    <>
      {/* Banner Section */}
      <section
        className="relative h-[500px] bg-cover bg-center lg:h-[600px]"
        style={{ backgroundImage: `url(${location.bannerImage})` }}
      >
        <div className="absolute inset-0 bg-black/50" />
        <div className="relative container mx-auto h-full px-4">
          <div className="flex h-full items-center">
            <div className="w-full lg:w-5/12">
              <h1 className="mt-12 text-4xl font-bold text-white md:text-5xl">{location.name}</h1>
              <div className="mt-5">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    if (mapSectionRef.current) {
                      mapSectionRef.current.scrollIntoView({ behavior: "smooth", block: "center" });
                    }
                  }}
                  className="hover:bg-opacity-90 font-helvetica inline-block rounded-full bg-white px-6 py-3 font-medium text-[#1c5534] transition-all"
                >
                  View Location
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="mb-32 py-20" ref={mapSectionRef}>
        <div className="container mx-auto px-4">
          <h3 className="mb-6 text-4xl font-bold text-[#1c5534] md:text-6xl">{location.name}</h3>
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-12">
            <div className="lg:col-span-7">
              {/* <p className="text-xl leading-relaxed text-gray-700">{location.description}</p> */}
              <div
                id="location-description"
                className="flex flex-col gap-2 text-xl leading-relaxed text-gray-700"
                dangerouslySetInnerHTML={{ __html: location.description }}
              ></div>
            </div>
            <div className="lg:col-span-5">
              <div className="overflow-hidden rounded-lg shadow-lg">
                <iframe
                  src={location.mapUrl}
                  width="100%"
                  height="462"
                  style={{ border: "none" }}
                  allowFullScreen
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default LocationSection;
